# 参考资料统一来源说明

## 资源概览
本目录整合了多个来源的AI工具、提示词和工作流程资源，旨在提供全面的AI编程助手规则集合。

## 各目录详细来源

### 1. 常用规则 (常用规则/)

#### 来自THXAN-2项目:
- **项目**: [THXAN-2](https://github.com/thxan/THXAN-2.git)
- **作者**: thxan (Cooperate with Powerest Gemini)
- **描述**: AI编程助手规则和提示词集合
- **包含内容**:
  - `主要提示词.md`: 核心AI助手提示词规则
  - `使用指南.md`: AI工具使用指南和最佳实践

#### 个人原创内容:
- **作者**: 项目维护者
- **包含内容**:
  - `提交规范.md`: 个人整理的Git提交格式规范

### 2. 提示词模板 (提示词模板/)
- **项目**: [猫娘v1.2和开发过程](https://linux.do/t/topic/740051)
- **平台**: Linux.do 社区
- **描述**: 猫娘风格AI编程助手提示词模板
- **包含内容**:
  - `猫娘v1.2.md`: 基础版猫娘风格提示词
  - `猫娘v2.1.md`: 改进版猫娘风格提示词
  - `猫娘开发提示词.md`: 开发专用猫娘提示词
- **特色**: 结合专业开发能力与趣味性交互，适用于小型项目维护和开发

### 3. AI-Prompts (AI-Prompts/)
- **项目**: [AI-Prompts](https://github.com/Blue-Soul-commits/AI-Prompts.git)
- **作者**: Blue-Soul-commits
- **描述**: AI提示词研究和实践集合
- **包含内容**:
  - `prompt-note.md`: 提示词使用笔记和经验总结
  - `Promptx工作机制.md`: Promptx工具的工作原理
  - `prompt增强版.md`: 增强版提示词技术
  - `提示词生成角色.md`: 提示词生成工具和方法
  - `罗小涵智能体特调提示词/`: 特殊智能体配置
- **研究重点**: 提示词工程技术、AI助手优化方法、智能体配置和调优

### 4. 工作流程 (工作流程/)
- **平台**: Linux.do 社区
- **主题**: Augment Agent 工具集标准化工作流提示词模板 - 开发调优
- **描述**: AI编程助手的标准化工作流程模板
- **包含内容**:
  - `Augment Code 工作模式规范 (2025-06-03).md`: 工具集标准化工作流提示词模板
  - `Augment Code 工作模式规范 (2025-06-18).md`: 核心工作模式规范（合并后的最新版本）
  - `Augment Code 工作模式规范 猫娘版(2025-06-23).md`: 猫娘风格的工作规范版本
  - `代码编辑任务工作流.md`: 代码编辑和修改的标准流程
  - `技术研究任务工作流.md`: 技术调研和学习的工作流程
  - `问题调试任务工作流.md`: 调试和问题解决的系统化方法
  - `项目部署任务工作流.md`: 项目部署和发布的流程规范
- **特点**: 标准化流程设计，适用于AI协作开发，覆盖开发全生命周期

### 5. 其他工具 (其他工具/)
- **包含工具**:
  - **Cunzhi (存知)**: 知识管理和存储工具，用于辅助AI工具的知识管理
- **特色**: 知识管理优化、信息检索增强、与AI工具集成

### 6. 其他资源
- `interactive-feedback mcp使用工作流.md`: MCP交互反馈工具的使用说明
- `其他参考.md`: 额外的参考资料和链接

## 主要来源平台
- **GitHub**: 开源项目和代码库
- **Linux.do**: 技术社区讨论和分享
- **个人收集**: 实践经验和工具整理

## 使用说明
1. 资源按功能和类型分类组织
2. 保持原始来源信息的完整性
3. 定期更新和维护内容
4. 遵循开源社区惯例

## 贡献原则
- 尊重原作者版权
- 标明资源来源和出处
- 保持内容的实用性和准确性
- 遵循开源社区惯例

## 更新记录
- 2025-07-30: 合并所有子目录来源信息，统一管理资源来源