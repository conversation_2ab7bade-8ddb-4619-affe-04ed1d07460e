#### 阶段1: 需求分析

- **render-mermaid**: 创建代码修改流程图
- **codebase-retrieval**: 了解要修改的代码模块
- **remember**: 记录修改目标和约束

#### 阶段2: 信息收集

- **view**: 查看目标文件当前状态
- **codebase-retrieval**: 查找相关代码和依赖
- **diagnostics**: 检查现有代码问题
- **web-search**: 搜索最佳实践和解决方案

#### 阶段3: 执行操作

- **str-replace-editor**: 进行精确代码修改
- **save-file**: 创建新的配置或测试文件（如需要）
- **launch-process**: 运行代码格式化工具

#### 阶段4: 验证结果

- **diagnostics**: 检查修改后的代码质量
- **launch-process**: 运行单元测试
- **read-process**: 查看测试结果
- **view**: 确认修改效果

#### 阶段5: 清理收尾

- **remove-files**: 删除临时测试文件
- **save-file**: 更新相关文档
- **remember**: 记录修改经验