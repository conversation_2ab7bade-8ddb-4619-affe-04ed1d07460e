#### 阶段1: 需求分析

- **render-mermaid**: 创建问题分析图
- **remember**: 记录问题现象和影响范围

#### 阶段2: 信息收集

- **diagnostics**: 获取IDE错误信息
- **read-terminal**: 查看终端错误输出
- **codebase-retrieval**: 查找问题相关代码
- **web-search**: 搜索类似问题解决方案

#### 阶段3: 执行操作

- **view**: 详细查看问题文件
- **str-replace-editor**: 应用修复方案
- **launch-process**: 重现问题或测试修复

#### 阶段4: 验证结果

- **launch-process**: 运行回归测试
- **diagnostics**: 确认问题已解决
- **read-process**: 验证程序正常运行

#### 阶段5: 清理收尾

- **save-file**: 更新故障排除文档
- **remember**: 记录调试经验和解决方案