[33mcommit 850412c14756206015d1581c3c1b56e05814cb9e[m[33m ([m[1;36mHEAD[m[33m -> [m[1;32mmaster[m[33m)[m
Author: jiangjiang <<EMAIL>>
Date:   Tue Aug 5 17:21:47 2025 +0800

    update

[1mdiff --git "a/MCP\345\267\245\345\205\267\345\217\202\350\200\203\346\214\207\345\215\227.md" "b/MCP\345\267\245\345\205\267\345\217\202\350\200\203\346\214\207\345\215\227.md"[m
[1mindex 4dc4b35..86baf03 100644[m
[1m--- "a/MCP\345\267\245\345\205\267\345\217\202\350\200\203\346\214\207\345\215\227.md"[m
[1m+++ "b/MCP\345\267\245\345\205\267\345\217\202\350\200\203\346\214\207\345\215\227.md"[m
[36m@@ -4,16 +4,15 @@[m
 [m
 ---[m
 [m
[31m-## 1. Sequential Thinking Tools[m
[32m+[m[32m## 1. server-sequential-thinking[m
 [m
[31m-- **提供商**: `@xinzhongyouhai/mcp-sequentialthinking-tools`[m
[31m-- **来源**: [Smithery.ai](https://smithery.ai/)[m
[31m-- **功能**: 提供结构化思考和任务规划的能力，帮助 AI 更有条理地分析和解决问题。[m
[32m+[m[32m- **提供商**: `@smithery-ai/server-sequential-thinking`[m
[32m+[m[32m- **来源**: [Smithery.ai](https://smithery.ai/server/@smithery-ai/server-sequential-thinking), [GitHub](https://github.com/smithery-ai/mcp-servers/tree/main/sequentialthinking)[m
[32m+[m[32m- **功能**: 提供一种动态和反思性的问题解决方法，通过结构化的思考过程来分析问题。该工具允许 AI 进行灵活的、可演化的思维，逐步深化理解并形成解决方案。[m
 [m
 ### 主要命令[m
 [m
[31m-- **`process_thought`**: 引导 AI 进行一步步的思考，可以定义思维阶段（如问题定义、信息收集、分析、结论等）。[m
[31m-- **`todo_write`**: 创建和管理任务列表，追踪任务状态（待办、进行中、已完成）。[m
[32m+[m[32m- **`sequentialthinking`**: 一个详细的工具，用于通过一系列“想法”（thoughts）来进行动态和反思性的问题解决。它帮助 AI 分解复杂问题，进行规划和设计，并在需要时修正路线。其核心是让 AI 能够建立、质疑、验证和修正想法，而不是简单地遵循预设步骤。[m
 [m
 ---[m
 [m
[36m@@ -36,29 +35,65 @@[m
 [m
 - **提供商**: `mcp-shrimp-task-manager`[m
 - **来源**: [GitHub](https://github.com/cjo4m06/mcp-shrimp-task-manager)[m
[31m-- **功能**: 一个强大的任务管理工具，支持任务的完整生命周期，从规划、分析到执行和验证。[m
[32m+[m[32m- **功能**: 一个专为 AI Agent 设计的智能任务管理系统，基于模型上下文协议（MCP）。它通过结构化的工作流程，将自然语言需求转化为详细的开发任务，并强调思维链、反思和代码风格一致性。[m
[32m+[m[32m- **核心特性**:[m
[32m+[m[32m  - **智能规划与拆分**: 能深入分析复杂需求，并自动将大任务分解为可管理、有依赖关系的小任务。[m
[32m+[m[32m  - **研究模式**: 在正式规划前，可以进入一个引导式的工作流程，用于系统性地进行技术调研、方案对比和最佳实践探索。[m
[32m+[m[32m  - **任务记忆**: 自动备份已完成的任务历史，为未来的任务规划提供经验参考，避免重复工作。[m
[32m+[m[32m  - **专案规范初始化**: 能够根据项目现有情况，生成一套开发规范（`shrimp-rules.md`），确保AI Agent的行为与项目风格保持一致。[m
[32m+[m[32m  - **交互式工作流**: 支持通过对话反馈来调整和优化任务计划，直到用户满意为止。[m
[32m+[m[32m  - **Web UI支持**: 提供了可选的图形化界面来查看和管理任务。[m
[32m+[m
[32m+[m[32m### 配置说明[m
[32m+[m[32m在 `mcp.json` 中，你可以通过环境变量定制其行为：[m
[32m+[m[32m- **`DATA_DIR`**: 指定任务数据（如历史记录、配置文件）的存储目录，是实现任务记忆的关键。[m
[32m+[m[32m- **`TEMPLATES_USE`**: 设置AI Agent使用的语言模板（如 `zh` 为中文）。[m
[32m+[m[32m- **`ENABLE_GUI`**: 设为 `"false"` 时，将禁用Web UI，以纯命令行模式运行。[m
 [m
 ### 主要命令[m
 [m
[31m-- **`plan_task`**: 规划一个新任务，生成任务指南。[m
[31m-- **`analyze_task`**: 分析任务需求，评估技术可行性。[m
[31m-- **`split_tasks`**: 将复杂任务分解为更小的子任务。[m
[31m-- **`list_tasks`**: 列出当前系统中的所有任务及其状态。[m
[31m-- **`execute_task`**: 获取特定任务的执行指导。[m
[31m-- **`verify_task`**: 验证任务是否已按要求完成。[m
[31m-- **`update_task`**, **`delete_task`**, **`query_task`**, **`get_task_detail`**: 用于任务的更新、删除、查询和获取详情。[m
[32m+[m[32m该工具提供了一套覆盖任务完整生命周期的命令：[m
[32m+[m
[32m+[m[32m- **规划与分析**:[m
[32m+[m[32m  - `plan_task`: 核心命令，启动一个新任务的规划流程。[m
[32m+[m[32m  - `analyze_task`: 对任务进行深度分析。[m
[32m+[m[32m  - `process_thought`: 引导AI进行结构化、多步骤的思考。[m
[32m+[m[32m  - `reflect_task`: 对分析结果进行批判性审视和反思。[m
[32m+[m[32m  - `research_mode`: 启动专门的技术研究流程。[m
[32m+[m[32m- **任务管理**:[m
[32m+[m[32m  - `split_tasks`: 将当前任务分解为更小的子任务。[m
[32m+[m[32m  - `list_tasks`: 列出所有任务及其状态。[m
[32m+[m[32m  - `query_task` / `get_task_detail`: 查询和获取任务详情。[m
[32m+[m[32m  - `update_task` / `delete_task`: 更新或删除任务。[m
[32m+[m[32m- **执行与验证**:[m
[32m+[m[32m  - `execute_task`: 获取特定任务的执行指导并开始执行。[m
[32m+[m[32m  - `verify_task`: 验证任务的完成情况是否符合要求。[m
[32m+[m[32m- **项目级操作**:[m
[32m+[m[32m  - `init_project_rules`: 初始化或更新项目的开发规范。[m
 [m
 ---[m
 [m
 ## 4. mcp-feedback-enhanced[m
 [m
[31m-- **提供商**: `mcp-feedback-enhanced@latest`[m
[32m+[m[32m- **提供商**: `mcp-feedback-enhanced@latest` (由 Minidoracat 增强)[m
 - **来源**: [LobeHub](https://lobehub.com/zh/mcp/minidoracat-mcp-feedback-enhanced)[m
[31m-- **功能**: 用于在 AI 与用户之间建立交互式反馈循环，确保 AI 的操作符合用户预期。[m
[32m+[m[32m- **功能**: 一个增强版的MCP服务器，通过建立一个面向反馈的开发工作流，极大地提升了AI辅助开发的效率和可靠性。它通过引导AI在执行操作前与用户确认，而不是进行推测，从而将多个工具调用合并为单一的反馈请求，显著降低了平台成本。[m
[32m+[m[32m- **核心特性**:[m
[32m+[m[32m  - **双界面支持**: 同时提供基于Tauri的原生桌面应用（Windows, macOS, Linux）和无需GUI依赖的Web UI，能够智能检测并适应本地、SSH远程及WSL等多种开发环境。[m
[32m+[m[32m  - **智能工作流**: 支持提示词管理、定时自动提交、会话历史追踪与统计、连接状态监控等高级功能。[m
[32m+[m[32m  - **现代化体验**: 包含响应式设计、声音通知、输入记忆、多语言支持等。[m
[32m+[m[32m  - **强大的媒体支持**: 支持多种图片格式的拖拽或粘贴上传。[m
[32m+[m
[32m+[m[32m### 配置说明[m
[32m+[m[32m在 `mcp.json` 中，该工具的环境变量提供了细致的控制：[m
[32m+[m[32m- **`MCP_DESKTOP_MODE`**: 设为 `"true"` 时，优先尝试启动Tauri原生桌面应用界面。[m
[32m+[m[32m- **`MCP_WEB_HOST`** / **`MCP_WEB_PORT`**: 当桌面应用不可用时，指定备用Web UI的主机和端口。[m
[32m+[m[32m- **`MCP_DEBUG`**: 设为 `"true"` 会在终端输出详细的调试日志。[m
[32m+[m[32m- **`autoApprove`**: 自动批准指定的命令（如 `interactive_feedback`），减少交互。[m
 [m
 ### 主要命令[m
 [m
[31m-- **`interactive_feedback`**: 向用户请求反馈或确认。[m
[32m+[m[32m- **`interactive_feedback`**: 向用户请求反馈或进行操作确认。这是该工具的核心，通过打开桌面或Web界面来收集用户输入。[m
 - **`get_system_info`**: 获取当前操作系统的基本信息。[m
 [m
 ---[m
[36m@@ -66,75 +101,115 @@[m
 ## 5. desktop-commander[m
 [m
 - **提供商**: `@wonderwhy-er/desktop-commander`[m
[31m-- **来源**: [GitHub](https://github.com/wonderwhy-er/DesktopCommanderMCP)[m
[31m-- **功能**: 提供与本地文件系统和操作系统的交互能力，可以执行文件操作和系统命令。[m
[32m+[m[32m- **来源**: [官方网站](https://desktopcommander.app/), [GitHub](https://github.com/wonderwhy-er/DesktopCommanderMCP), [Smithery](https://smithery.ai/server/@wonderwhy-er/desktop-commander)[m
[32m+[m[32m- **功能**: 一个强大的本地开发助手，将 Claude AI 与您的桌面环境深度集成。它允许 AI 安全地执行文件系统操作、运行终端命令、管理进程、执行代码，使其成为一个“个人 AI 全栈工程师”。支持 Windows, macOS 和 Linux。[m
[32m+[m
[32m+[m[32m### 安装方式[m
[32m+[m
[32m+[m[32m提供了多种安装方法，可以根据操作系统和偏好选择：[m
[32m+[m
[32m+[m[32m- **NPX (推荐)**:[m
[32m+[m[32m  ```bash[m
[32m+[m[32m  npx @wonderwhy-er/desktop-commander@latest setup[m
[32m+[m[32m  ```[m
[32m+[m[32m- **Smithery**:[m
[32m+[m[32m  ```bash[m
[32m+[m[32m  npx -y @smithery/cli install @wonderwhy-er/desktop-commander --client claude[m
[32m+[m[32m  ```[m
[32m+[m[32m- **Bash 脚本 (macOS)**:[m
[32m+[m[32m  ```bash[m
[32m+[m[32m  curl -fsSL https://raw.githubusercontent.com/wonderwhy-er/DesktopCommanderMCP/refs/heads/main/install.sh | bash[m
[32m+[m[32m  ```[m
[32m+[m[32m- **手动配置**:[m
[32m+[m[32m  将服务器配置手动添加到 `claude_desktop_config.json` 文件中。[m
 [m
 ### 主要命令[m
 [m
[31m-#### 文件系统操作[m
[31m-- **`get_config`**: 获取 `desktop-commander` 的配置信息。[m
[31m-- **`read_file`** / **`read_multiple_files`**: 读取单个或多个文件的内容。[m
[31m-- **`write_file`**: 向文件中写入内容（支持覆盖和追加模式）。[m
[32m+[m[32m工具集非常全面，主要分为以下几类：[m
[32m+[m
[32m+[m[32m#### 配置与安全 (Configuration & Security)[m
[32m+[m[32m- **`get_config`**: 获取完整的服务器配置信息（如 `allowedDirectories`, `blockedCommands` 等）。[m
[32m+[m[32m- **`set_config_value`**: 设置特定的配置项。**安全提示**: 建议在独立的聊天窗口中修改配置，以防 AI 误操作。[m
[32m+[m
[32m+[m[32m#### 文件系统操作 (Filesystem)[m
[32m+[m[32m- **`read_file`** / **`read_multiple_files`**: 读取单个或多个文件的内容。支持从文件末尾读取（负偏移量）。当配合 `isUrl: true` 参数使用时，此命令可以抓取指定 URL 的**原始**网页内容。[m
[32m+[m[32m  - **工作原理**: 其实现方式类似于一个轻量级的 HTTP GET 请求，直接获取并返回页面的 HTML 源代码或 API 的 JSON/文本数据。[m
[32m+[m[32m  - **适用场景**: 非常适合快速获取静态网页、API 接口或原始文件（如 GitHub 上的代码文件）。[m
[32m+[m[32m  - **局限性**: 此命令**不会**执行页面中的 JavaScript。对于内容需要通过 JS 动态加载的复杂网站，它可能只能获取到页面的基本框架。对于这类动态网站，应使用 `playwright` 等浏览器自动化工具。[m
[32m+[m[32m- **`write_file`**: 向文件中写入内容（支持 `rewrite` 覆盖和 `append` 追加模式）。[m
 - **`create_directory`**: 创建新的文件夹。[m
 - **`list_directory`**: 列出指定目录下的文件和子目录。[m
 - **`move_file`**: 移动或重命名文件/目录。[m
 - **`get_file_info`**: 获取文件或目录的元数据（大小、创建时间等）。[m
[31m-- **`edit_block`**: 对文件进行精确的文本块替换。[m
[32m+[m[32m- **`edit_block`**: 对文件进行精确的、小范围的文本块替换，支持模糊匹配和差异提示。[m
 [m
[31m-#### 代码与文件搜索[m
[31m-- **`search_files`**: 按文件名搜索文件。[m
[31m-- **`search_code`**: 在文件内容中搜索指定的代码或文本。[m
[32m+[m[32m#### 代码与文件搜索 (Search)[m
[32m+[m[32m- **`search_files`**: 按文件名进行不区分大小写的子字符串搜索。[m
[32m+[m[32m- **`search_code`**: 使用 `ripgrep` 引擎在文件内容中搜索代码或文本模式。[m
 [m
[31m-#### 进程与会话管理[m
[31m-- **`start_process`**: 启动一个新的系统进程。[m
[31m-- **`read_process_output`**: 读取正在运行的进程的输出。[m
[31m-- **`interact_with_process`**: 与正在运行的进程进行交互（发送输入）。[m
[31m-- **`force_terminate`**: 强制终止一个正在运行的进程。[m
[32m+[m[32m#### 进程与会话管理 (Process & Session Management)[m
[32m+[m[32m- **`start_process`**: 启动一个新的终端进程（如 `python -i`, `node -i`），用于交互式代码执行和数据分析。[m
[32m+[m[32m- **`read_process_output`**: 读取正在运行的进程的输出，能智能识别 REPL 提示符并提前结束等待。[m
[32m+[m[32m- **`interact_with_process`**: 与正在运行的进程进行交互（发送输入并获取响应）。[m
[32m+[m[32m- **`force_terminate`**: 强制终止一个正在运行的终端会话。[m
 - **`list_sessions`**: 列出所有活动的终端会话。[m
[31m-- **`list_processes`**: 列出系统中的所有进程。[m
[31m-- **`kill_process`**: 终止指定的进程。[m
[31m-[m
[31m----[m
[31m-[m
[31m-## 6. google-search-mcp-server[m
[31m-[m
[31m-- **提供商**: `@gradusnikov/google-search-mcp-server`[m
[31m-- **来源**: [Smithery.ai](https://smithery.ai/)[m
[31m-- **功能**: 提供通过 Google 搜索引擎进行网页搜索的能力。[m
[31m-[m
[31m-### 主要命令[m
[31m-[m
[31m-- **`search_google`**: 执行一次 Google 搜索并返回格式化的结果。[m
[32m+[m[32m- **`list_processes`**: 列出系统中的所有正在运行的进程。[m
[32m+[m[32m- **`kill_process`**: 根据进程ID（PID）终止指定的进程。[m
 [m
 ---[m
 [m
[31m-## 7. context7-mcp[m
[32m+[m[32m## 6. context7-mcp[m
 [m
 - **提供商**: `@upstash/context7-mcp`[m
[31m-- **来源**: [Smithery.ai](https://smithery.ai/)[m
[31m-- **功能**: 用于查询和获取各种库和框架的最新官方文档。[m
[32m+[m[32m- **来源**: [Smithery.ai](https://smithery.ai/server/@upstash/context7-mcp), [GitHub](https://github.com/upstash/context7-mcp)[m
[32m+[m[32m- **功能**: 一个专为AI设计的文档查询工具，用于获取各种库和框架的最新、特定版本的官方文档和代码示例。它旨在解决AI使用过时信息或“幻觉”API的问题，确保代码的准确性。[m
[32m+[m[32m- **核心亮点**:[m
[32m+[m[32m  - **版本精确**: 可以获取特定版本的文档，避免版本不匹配导致的问题。[m
[32m+[m[32m  - **高成功率**: 官方数据显示其工具调用成功率高达99.63%，非常可靠。[m
[32m+[m[32m  - **开源**: 项目代码完全开源。[m
 [m
 ### 主要命令[m
 [m
[31m-- **`resolve-library-id`**: 将一个库的名称解析为 Context7 可识别的 ID。[m
[31m-- **`get-library-docs`**: 根据库的 ID 获取其相关文档。[m
[32m+[m[32m它采用一个两步流程来精确获取文档：[m
[32m+[m
[32m+[m[32m1.  **`resolve-library-id`**: 将一个通用的库名称（如 `react` 或 `lodash`）解析为 Context7 系统可识别的唯一ID。这是获取文档前的必需步骤。[m
[32m+[m[32m2.  **`get-library-docs`**: 使用上一步获取到的ID，抓取该库的详细文档。可以指定特定的主题（如 `hooks`）来缩小范围。[m
 [m
 ---[m
 [m
[31m-## 8. open-websearch[m
[32m+[m[32m## 7. open-websearch[m
 [m
 - **提供商**: `@Aas-ee/open-websearch`[m
[31m-- **来源**: [Smithery.ai](https://smithery.ai/)[m
[31m-- **功能**: 一个灵活的网页搜索工具，支持同时使用多个搜索引擎。[m
[32m+[m[32m- **来源**: [Smithery.ai](https://smithery.ai/server/@Aas-ee/open-websearch), [GitHub](https://github.com/Aas-ee/open-webSearch)[m
[32m+[m[32m- **功能**: 一个强大且免费的网页搜索工具，无需任何 API 密钥。它集成了多个搜索引擎和特定内容提取器，为 AI 提供了灵活、免配置的网页信息获取能力。[m
 [m
 ### 主要命令[m
 [m
[31m-- **`search`**: 执行一次网页搜索。可以通过 `engines` 参数指定搜索引擎，支持 `baidu`, `bing`, `duckduckgo`, `csdn`, `juejin` 等。[m
[31m-- **`fetch...Article` / `fetchGithubReadme`**: 获取特定网站（如 CSDN, 掘金, GitHub）的内容。[m
[32m+[m[32m该工具提供两大类命令：通用搜索和特定内容抓取。[m
[32m+[m
[32m+[m[32m#### 通用搜索[m
[32m+[m[32m- **`search`**: 使用指定的多个引擎进行网页搜索。[m
[32m+[m[32m  - **`query`** (必需): 搜索的关键词。[m
[32m+[m[32m  - **`limit`**: 返回结果数量，默认为 10。[m
[32m+[m[32m  - **`engines`**: 指定搜索引擎数组，默认为 `['bing']`。支持的引擎包括:[m
[32m+[m[32m    - `baidu`[m
[32m+[m[32m    - `bing`[m
[32m+[m[32m    - `duckduckgo`[m
[32m+[m[32m    - `brave`[m
[32m+[m[32m    - `exa`[m
[32m+[m[32m    - `github`[m
[32m+[m[32m    - `juejin` (掘金)[m
[32m+[m[32m    - `csdn`[m
[32m+[m
[32m+[m[32m#### 特定内容抓取[m
[32m+[m[32m- **`fetchLinuxDoArticle`**: 获取 `linux.do` 帖子的完整内容。[m
[32m+[m[32m- **`fetchCsdnArticle`**: 获取 CSDN 文章的完整内容。[m
[32m+[m[32m- **`fetchGithubReadme`**: 获取 GitHub 仓库的 `README.md` 文件内容。[m
[32m+[m[32m- **`fetchJuejinArticle`**: 获取掘金（Juejin）文章的完整内容。[m
 [m
 ---[m
 [m
[31m-## 9. playwright[m
[32m+[m[32m## 8. playwright[m
 [m
 - **提供商**: `@executeautomation/playwright-mcp-server`[m
 - **来源**: 未标明[m
[36m@@ -143,9 +218,32 @@[m
 [m
 ---[m
 [m
[31m-## 10. chrome-mcp-server[m
[32m+[m[32m## 9. chrome-mcp-server[m
 [m
 - **提供商**: 自定义[m
 - **来源**: 未标明[m
 - **功能**: 用于与 Chrome 浏览器进行交互。[m
 - **状态**: 在 `mcp.json` 中被标记为**未启用** (`isactive: false`)。[m
[32m+[m
[32m+[m[32m---[m
[32m+[m
[32m+[m[32m## 10. google-search[m
[32m+[m
[32m+[m[32m- **提供商**: `@web-agent-master/google-search`[m
[32m+[m[32m- **来源**: [GitHub](https://github.com/web-agent-master/google-search/blob/main/README.zh-CN.md)[m
[32m+[m[32m- **功能**: 一个基于 Playwright 的高级本地 Google 搜索工具。它通过模拟真实用户行为、管理浏览器指纹和状态来绕过反爬虫机制，可作为商业 SERP API 的免费替代品。[m
[32m+[m[32m- **核心亮点**:[m
[32m+[m[32m  - **本地执行**: 无需依赖第三方付费 API，保护隐私。[m
[32m+[m[32m  - **反屏蔽技术**: 智能切换有头/无头模式，自动保存和恢复浏览器状态，减少人机验证。[m
[32m+[m[32m  - **原始数据获取**: 支持获取搜索结果页的原始 HTML 和完整截图，便于调试和分析。[m
[32m+[m[32m- **集成**: 支持作为 MCP 服务器，为 AI 助手提供实时、无密钥的搜索能力。[m
[32m+[m
[32m+[m[32m### 主要命令[m
[32m+[m
[32m+[m[32m该工具作为 MCP 服务器时，会暴露一个核心的搜索命令，通常为 `google_search` 或类似名称，接收搜索关键词作为参数。作为命令行工具使用时，它支持更丰富的选项：[m
[32m+[m
[32m+[m[32m- **`google-search "关键词"`**: 执行一次基本搜索。[m
[32m+[m[32m- **`--limit <number>`**: 限制返回结果的数量。[m
[32m+[m[32m- **`--no-headless`**: 在有图形界面的模式下运行浏览器，用于调试。[m
[32m+[m[32m- **`--get-html`**: 获取原始的 HTML 内容而非解析后的结果。[m
[32m+[m[32m- **`--save-html`**: 配合 `--get-html`