{"mcpServers": {"desktop-commander": {"command": "npx", "args": ["-y", "@wonderwhy-er/desktop-commander"]}, "mcp-feedback-enhanced": {"command": "uvx", "args": ["mcp-feedback-enhanced@latest"], "timeout": 600, "env": {"MCP_DESKTOP_MODE": "true", "MCP_WEB_HOST": "127.0.0.1", "MCP_WEB_PORT": "8765", "MCP_DEBUG": "false"}, "autoApprove": ["interactive_feedback"]}, "shrimp-task-manager": {"command": "npx", "args": ["-y", "mcp-shrimp-task-manager"], "env": {"DATA_DIR": "C:\\Users\\<USER>\\Desktop\\cursor-shrimp", "TEMPLATES_USE": "zh", "ENABLE_GUI": "false"}}, "server-sequential-thinking": {"command": "cmd", "args": ["/c", "npx", "-y", "@smithery/cli@latest", "run", "@smithery-ai/server-sequential-thinking", "--key", "214acdef-eba5-4cf8-badc-271d310f4c85", "--profile", "parallel-sawfish-p0jW9m"]}, "google-search": {"command": "cmd", "args": ["/c", "npx", "google-search-mcp"]}, "open-websearch": {"command": "cmd", "args": ["/c", "npx", "-y", "@smithery/cli@latest", "run", "@Aas-ee/open-websearch", "--key", "214acdef-eba5-4cf8-badc-271d310f4c85", "--profile", "parallel-sawfish-p0jW9m"]}, "deepwiki": {"type": "sse", "url": "https://mcp.deepwiki.com/sse"}, "context7-mcp": {"command": "cmd", "args": ["/c", "npx", "-y", "@smithery/cli@latest", "run", "@upstash/context7-mcp", "--key", "214acdef-eba5-4cf8-badc-271d310f4c85"]}, "playwright": {"type": "stdio", "command": "npx", "args": ["-y", "@executeautomation/playwright-mcp-server"]}, "chrome-mcp-server": {"isactive": false, "type": "streamableHttp", "url": "http://127.0.0.1:12306/mcp"}}}