<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="53bca7a6-a998-4a35-ab70-dab1c50302f0" name="更改" comment="update">
      <change beforePath="$PROJECT_DIR$/.cursor/rules/Neko-Dev Collaborative Rule-2.md" beforeDir="false" afterPath="$PROJECT_DIR$/.cursor/rules/Neko-Dev Collaborative Rule-2.md" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="GitHubPullRequestSearchHistory">{
  &quot;lastFilter&quot;: {
    &quot;state&quot;: &quot;OPEN&quot;,
    &quot;assignee&quot;: &quot;huazz233&quot;
  }
}</component>
  <component name="GithubPullRequestsUISettings">{
  &quot;selectedUrlAndAccountId&quot;: {
    &quot;url&quot;: &quot;https://github.com/huazz233/huazz-rules.git&quot;,
    &quot;accountId&quot;: &quot;8a1340bc-98c1-467f-aa42-dddc91076c64&quot;
  }
}</component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 5
}</component>
  <component name="ProjectId" id="30a8CGMyimOT8s4D3YQNuPoIvmZ" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "git-widget-placeholder": "master",
    "junie.onboarding.icon.badge.shown": "true",
    "last_opened_file_path": "C:/Users/<USER>/Desktop/huazz-rules/.cursor/rules",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.stylelint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.stylelint": "",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="C:\Users\<USER>\Desktop\huazz-rules\.cursor\rules" />
      <recent name="C:\Users\<USER>\Desktop\huazz-rules\2025\8\4" />
      <recent name="C:\Users\<USER>\Desktop\huazz-rules\2025\8" />
      <recent name="C:\Users\<USER>\Desktop\huazz-rules" />
      <recent name="C:\Users\<USER>\Desktop\huazz-rules\参考\工作流" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="C:\Users\<USER>\Desktop\huazz-rules\2025\8\5" />
      <recent name="C:\Users\<USER>\Desktop\huazz-rules\2025\8\4" />
      <recent name="C:\Users\<USER>\Desktop\huazz-rules\2025\7\30" />
      <recent name="C:\Users\<USER>\Desktop\huazz-rules" />
      <recent name="C:\Users\<USER>\Desktop\huazz-rules\参考" />
    </key>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-6a121458b545-JavaScript-PY-251.25410.122" />
        <option value="bundled-python-sdk-880ecab49056-36ea0e71a18c-com.jetbrains.pycharm.pro.sharedIndexes.bundled-PY-251.25410.122" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="53bca7a6-a998-4a35-ab70-dab1c50302f0" name="更改" comment="" />
      <created>1753854194159</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1753854194159</updated>
      <workItem from="1753854195270" duration="17167000" />
      <workItem from="1753944524559" duration="1851000" />
      <workItem from="1754291939493" duration="4297000" />
      <workItem from="1754358588565" duration="18902000" />
      <workItem from="1754398392651" duration="4569000" />
    </task>
    <task id="LOCAL-00001" summary="add ..">
      <option name="closed" value="true" />
      <created>1753856181859</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1753856181859</updated>
    </task>
    <task id="LOCAL-00002" summary="添加目前的mcp配置">
      <option name="closed" value="true" />
      <created>1753858473169</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1753858473169</updated>
    </task>
    <task id="LOCAL-00003" summary="整理">
      <option name="closed" value="true" />
      <created>1753861327484</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1753861327484</updated>
    </task>
    <task id="LOCAL-00004" summary="111">
      <option name="closed" value="true" />
      <created>1753865950972</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1753865950972</updated>
    </task>
    <task id="LOCAL-00005" summary="111">
      <option name="closed" value="true" />
      <created>1753866169059</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1753866169059</updated>
    </task>
    <task id="LOCAL-00006" summary="111">
      <option name="closed" value="true" />
      <created>1753866290003</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1753866290003</updated>
    </task>
    <task id="LOCAL-00007" summary="111">
      <option name="closed" value="true" />
      <created>1753869473209</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1753869473209</updated>
    </task>
    <task id="LOCAL-00008" summary="111">
      <option name="closed" value="true" />
      <created>1753869921042</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1753869921042</updated>
    </task>
    <task id="LOCAL-00009" summary="111">
      <option name="closed" value="true" />
      <created>1753870718993</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1753870718993</updated>
    </task>
    <task id="LOCAL-00010" summary="add .cursor">
      <option name="closed" value="true" />
      <created>1753871503971</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1753871503971</updated>
    </task>
    <task id="LOCAL-00011" summary="update">
      <option name="closed" value="true" />
      <created>1753945468966</created>
      <option name="number" value="00011" />
      <option name="presentableId" value="LOCAL-00011" />
      <option name="project" value="LOCAL" />
      <updated>1753945468966</updated>
    </task>
    <task id="LOCAL-00012" summary="update">
      <option name="closed" value="true" />
      <created>1754294061247</created>
      <option name="number" value="00012" />
      <option name="presentableId" value="LOCAL-00012" />
      <option name="project" value="LOCAL" />
      <updated>1754294061247</updated>
    </task>
    <task id="LOCAL-00013" summary="update">
      <option name="closed" value="true" />
      <created>1754295309681</created>
      <option name="number" value="00013" />
      <option name="presentableId" value="LOCAL-00013" />
      <option name="project" value="LOCAL" />
      <updated>1754295309681</updated>
    </task>
    <task id="LOCAL-00014" summary="update">
      <option name="closed" value="true" />
      <created>1754378721737</created>
      <option name="number" value="00014" />
      <option name="presentableId" value="LOCAL-00014" />
      <option name="project" value="LOCAL" />
      <updated>1754378721737</updated>
    </task>
    <task id="LOCAL-00015" summary="update">
      <option name="closed" value="true" />
      <created>1754385707940</created>
      <option name="number" value="00015" />
      <option name="presentableId" value="LOCAL-00015" />
      <option name="project" value="LOCAL" />
      <updated>1754385707940</updated>
    </task>
    <task id="LOCAL-00016" summary="update1">
      <option name="closed" value="true" />
      <created>1754388589874</created>
      <option name="number" value="00016" />
      <option name="presentableId" value="LOCAL-00016" />
      <option name="project" value="LOCAL" />
      <updated>1754388589874</updated>
    </task>
    <task id="LOCAL-00017" summary="update2">
      <option name="closed" value="true" />
      <created>1754399151968</created>
      <option name="number" value="00017" />
      <option name="presentableId" value="LOCAL-00017" />
      <option name="project" value="LOCAL" />
      <updated>1754399151968</updated>
    </task>
    <task id="LOCAL-00018" summary="update3">
      <option name="closed" value="true" />
      <created>1754401465763</created>
      <option name="number" value="00018" />
      <option name="presentableId" value="LOCAL-00018" />
      <option name="project" value="LOCAL" />
      <updated>1754401465763</updated>
    </task>
    <task id="LOCAL-00019" summary="update">
      <option name="closed" value="true" />
      <created>1754402440788</created>
      <option name="number" value="00019" />
      <option name="presentableId" value="LOCAL-00019" />
      <option name="project" value="LOCAL" />
      <updated>1754402440788</updated>
    </task>
    <option name="localTasksCounter" value="20" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="refactor(docs): 重构提交规范文档为AI工具执行指南&#10;&#10;将原有的团队Git协作规范重构为专门的AI助手工作指南，明确定义了AI在分析提交和生成规范信息时的执行原则、知识库和工作流程。&#10;&#10;1. 文档结构重组: 采用执行原则、知识库、记忆机制、使用方式四个核心部分&#10;2. 功能重新定位: 从团队规范转为AI工具的专用执行指南&#10;3. 工作流标准化: 明确定义了从接收输入到生成输出的完整流程&#10;4. 输出格式规范: 统一了提交信息和代码审查报告的标准格式&#10;5. 使用指南完善: 区分最新提交和历史提交的不同修改方式" />
    <MESSAGE value="chore(docs): 删除提交规范文档&#10;&#10;移除了参考资料目录下的AI提交分析规范文档，该文件包含了AI工具的执行原则、知识库定义、记忆机制和使用方式等内容。&#10;&#10;1. 文件删除: 完全移除 参考资料/常用规则/提交规范.md 文件&#10;2. 内容清理: 删除了95行的AI工具执行指南内容&#10;3. 目录整理: 清理常用规则目录下的相关文档" />
    <MESSAGE value="refactor(docs): 重命名提交规范文档以准确反映其功能定位" />
    <MESSAGE value="refactor(docs): 重命名提交规范文档以准确反映其功能定位" />
    <MESSAGE value="docs: 添加AI助手核心规则和参考文档" />
    <MESSAGE value="docs: 添加AI助手核心规则和参考文档" />
    <MESSAGE value="docs: 新增AI开发规范文档集合并优化现有规则" />
    <MESSAGE value="docs: 新增AI开发规范文档集合并优化现有规则" />
    <MESSAGE value="add .cursor" />
    <MESSAGE value="feat(rules): 增加AI助手可用性监控与报告机制&#10;&#10;为两个AI助手规则文件添加了完整的可用性监控功能，提升用户体验和问题诊断能力。&#10;&#10;关键变更点：&#10;1. 身份定义增强：在核心身份中明确&quot;时刻关注自身可用性&quot;的职责&#10;2. 新增可用性报告原则：定义MCP工具连接失败时的统一处理流程&#10;3. 工作流产出完善：为计划、优化、评审阶段补充明确的产出说明&#10;4. 统一状态报告区块：在文件末尾添加标准化的可用性状态模板&#10;5. 文件重命名：简化协作式开发规则文件名，去除&quot;猫娘&quot;标识" />
    <MESSAGE value="feat(rules): 增加AI助手可用性监控与报告机制&#10;&#10;为两个AI助手规则文件添加了完整的可用性监控功能，提升用户体验和问题诊断能力。&#10;&#10;关键变更点：&#10;1. 身份定义增强：在核心身份中明确&quot;时刻关注自身可用性&quot;的职责&#10;2. 新增可用性报告原则：定义MCP工具连接失败时的统一处理流程&#10;3. 工作流产出完善：为计划、优化、评审阶段补充明确的产出说明&#10;4. 统一状态报告区块：在文件末尾添加标准化的可用性状态模板&#10;5. 文件重命名：简化协作式开发规则文件名，去除&quot;猫娘&quot;标识" />
    <MESSAGE value="feat(rules): 增强AI助手工具调用灵活性，实现任务驱动的工具使用模式&#10;&#10;本次变更解决了MCP工具调用频率低和规则僵化的问题，通过引入&quot;任务驱动&quot;原则，&#10;让AI助手能够在任何工作阶段根据具体任务需要灵活调用工具，而不再受模式限制。&#10;&#10;关键变更点：&#10;1. 新增第7条行动准则&quot;任务驱动&quot;：允许跨模式灵活调用MCP工具&#10;2. 增强各工作模式：研究、构思、计划模式现在都可以使用desktop-commander等工具&#10;3. 集成记忆机制：新增remember和recall工具，实现经验积累和学习能力&#10;4. 优化交互格式：规范化为&quot;（动作）语言 【附加信息】&quot;的表达方式&#10;5. 完善文档记录：新增完整的增强过程文档，包含问题分析和解决方案" />
    <MESSAGE value="feat(rules): 增强AI助手工具调用灵活性，实现任务驱动的工具使用模式&#10;&#10;本次变更解决了MCP工具调用频率低和规则僵化的问题，通过引入&quot;任务驱动&quot;原则，&#10;让AI助手能够在任何工作阶段根据具体任务需要灵活调用工具，而不再受模式限制。&#10;&#10;关键变更点：&#10;1. 新增第7条行动准则&quot;任务驱动&quot;：允许跨模式灵活调用MCP工具&#10;2. 增强各工作模式：研究、构思、计划模式现在都可以使用desktop-commander等工具&#10;3. 集成记忆机制：新增remember和recall工具，实现经验积累和学习能力&#10;4. 优化交互格式：规范化为&quot;（动作）语言 【附加信息】&quot;的表达方式&#10;5. 完善文档记录：新增完整的增强过程文档，包含问题分析和解决方案" />
    <MESSAGE value="docs(mcp): 新增MCP工具参考指南&#10;&#10;为方便查阅和使用MCP（小喵的百宝袋）工具箱，新增此参考指南。&#10;&#10;该指南详细说明了各个工具的功能、使用场景和注意事项，有助于提高开发效率和规范工具的使用。" />
    <MESSAGE value="docs(mcp): 新增MCP工具参考指南&#10;&#10;为方便查阅和使用MCP（小喵的百宝袋）工具箱，新增此参考指南。&#10;&#10;该指南详细说明了各个工具的功能、使用场景和注意事项，有助于提高开发效率和规范工具的使用。" />
    <MESSAGE value="refactor(mcp): 重构MCP工具配置并更新说明文档&#10;&#10;对 `mcp.json` 和相关文档进行了大规模重构，以提高工具配置的清晰度、一致性和可维护性。&#10;&#10;主要变更包括：&#10;- **配置重命名**: 将 `google-search-mcp-server` 重命名为 `server-sequential-thinking`，以更准确地反映其功能。&#10;- **配置移除**: 删除了废弃的 `Sequential Thinking Tools` 配置。&#10;- **文档更新 (`MCP工具参考指南.md`)**:&#10;  - 全面更新了 `desktop-commander` 和 `shrimp-task-manager` 的功能描述，使其更加详细和准确。&#10;  - 详细阐述了 `open-websearch` 的多引擎搜索能力和特定内容抓取功能。&#10;  - 补充了 `google-search` 的命令行用法和高级选项。&#10;- **`README.md` 同步**: 相应地更新了 `README.md` 中的工具列表。" />
    <MESSAGE value="docs(mcp): 优化MCP工具指南文档格式&#10;&#10;移除了文件顶部多余的分割线和空行，使文档结构更加简洁清晰。" />
    <MESSAGE value="refactor(mcp): 重构MCP工具配置并更新说明文档&#10;&#10;对 `mcp.json` 和相关文档进行了大规模重构，以提高工具配置的清晰度、一致性和可维护性。&#10;&#10;主要变更包括：&#10;- **配置重命名**: 将 `google-search-mcp-server` 重命名为 `server-sequential-thinking`，以更准确地反映其功能。&#10;- **配置移除**: 删除了废弃的 `Sequential Thinking Tools` 配置。&#10;- **文档更新 (`MCP工具参考指南.md`)**:&#10;  - 全面更新了 `desktop-commander` 和 `shrimp-task-manager` 的功能描述，使其更加详细和准确。&#10;  - 详细阐述了 `open-websearch` 的多引擎搜索能力和特定内容抓取功能。&#10;  - 补充了 `google-search` 的命令行用法和高级选项。&#10;- **`README.md` 同步**: 相应地更新了 `README.md` 中的工具列表。" />
    <MESSAGE value="refactor(docs): 重构协作式开发规则为PromptX核心版&#10;&#10;将原有的详细协作式开发规则文档重构为更简洁的PromptX核心版本，优化了文档结构和内容组织。&#10;&#10;关键变更点：&#10;1. 文档标题更新：添加&quot;PromptX核心版&quot;标识，明确版本定位&#10;2. 角色定义优化：引入DPML框架（人格-原则-知识），使角色定义更加结构化&#10;3. 核心准则精简：从7条优化为6条，突出记忆自动加载和智能回调机制&#10;4. 工作流程简化：保留6个核心模式，但简化了每个模式的描述和行动步骤&#10;5. 移除冗余内容：删除了详细的MCP工具使用指南和可用性状态说明&#10;6. 特殊模式精简：从3个特殊模式简化为2个，保留最核心的快速模式和调试模式" />
    <MESSAGE value="feat(docs): 恢复并优化协作式开发规则为完整版本&#10;&#10;将协作式开发规则从简化的PromptX核心版恢复为功能完整的版本，并新增了详细的任务档案化机制和记忆管理策略。&#10;&#10;关键变更点：&#10;1. 文档结构恢复：从66行的精简版扩展为98行的完整版，恢复了详细的工作流程说明&#10;2. 记忆机制升级：从抽象的&quot;专业记忆库&quot;概念转换为具体的&quot;.neko-memory&quot;文件夹物理存储方案&#10;3. 工作流程细化：为每个模式添加了更详细的行动步骤和工具调用说明&#10;4. 新增任务档案：在评审阶段增加了&quot;撰写日记&quot;功能，将高价值任务保存为时间戳格式的Markdown文件&#10;5. 搜索策略优化：明确了&quot;猫娘情报网&quot;的搜索优先级，优先使用google-search进行全网搜寻&#10;6. 新增对话记录：添加了完整的2072行Gemini对话记录，记录了规则演进的完整过程" />
    <MESSAGE value="feat(docs): 创建新版协作规则并重构文件命名&#10;&#10;创建了全新的Neko-Dev Collaborative Rule-2.md文档，采用DPML框架重新设计协作规则，并将原有中文文件名重命名为英文格式。&#10;&#10;关键变更点：&#10;1. 新增规则文档：创建了108行的全新协作规则文档，采用DPML（人格-原则-知识）框架&#10;2. 文件重命名：将原有中文文件名&quot;协作式开发规则 (Neko-Dev Collaborative Rule).md&quot;重命名为&quot;Neko-Dev Collaborative Rule.md&quot;&#10;3. 架构重新设计：引入&quot;动态工具调用&quot;作为最高准则，强调工具不被模式锁定&#10;4. 工作流优化：将工作流描述改为&quot;只谈做什么，不谈用什么工具&quot;的抽象化设计&#10;5. 工具清单完善：新增&quot;军火库&quot;章节，提供基于《MCP工具参考指南》的完整工具族清单&#10;6. 记忆机制具体化：明确使用&quot;.neko-memory&quot;作为猫娘日记的存储方案" />
    <MESSAGE value="refactor(docs): 重构协作式开发规则为PromptX核心版&#10;&#10;将原有的详细协作式开发规则文档重构为更简洁的PromptX核心版本，优化了文档结构和内容组织。&#10;&#10;关键变更点：&#10;1. 文档标题更新：添加&quot;PromptX核心版&quot;标识，明确版本定位&#10;2. 角色定义优化：引入DPML框架（人格-原则-知识），使角色定义更加结构化&#10;3. 核心准则精简：从7条优化为6条，突出记忆自动加载和智能回调机制&#10;4. 工作流程简化：保留6个核心模式，但简化了每个模式的描述和行动步骤&#10;5. 移除冗余内容：删除了详细的MCP工具使用指南和可用性状态说明&#10;6. 特殊模式精简：从3个特殊模式简化为2个，保留最核心的快速模式和调试模式" />
    <MESSAGE value="feat(docs): 恢复并优化协作式开发规则为完整版本&#10;&#10;将协作式开发规则从简化的PromptX核心版恢复为功能完整的版本，并新增了详细的任务档案化机制和记忆管理策略。&#10;&#10;关键变更点：&#10;1. 文档结构恢复：从66行的精简版扩展为98行的完整版，恢复了详细的工作流程说明&#10;2. 记忆机制升级：从抽象的&quot;专业记忆库&quot;概念转换为具体的&quot;.neko-memory&quot;文件夹物理存储方案&#10;3. 工作流程细化：为每个模式添加了更详细的行动步骤和工具调用说明&#10;4. 新增任务档案：在评审阶段增加了&quot;撰写日记&quot;功能，将高价值任务保存为时间戳格式的Markdown文件&#10;5. 搜索策略优化：明确了&quot;猫娘情报网&quot;的搜索优先级，优先使用google-search进行全网搜寻&#10;6. 新增对话记录：添加了完整的2072行Gemini对话记录，记录了规则演进的完整过程" />
    <MESSAGE value="feat(docs): 创建新版协作规则并重构文件命名&#10;&#10;创建了全新的Neko-Dev Collaborative Rule-2.md文档，采用DPML框架重新设计协作规则，并将原有中文文件名重命名为英文格式。&#10;&#10;关键变更点：&#10;1. 新增规则文档：创建了108行的全新协作规则文档，采用DPML（人格-原则-知识）框架&#10;2. 文件重命名：将原有中文文件名&quot;协作式开发规则 (Neko-Dev Collaborative Rule).md&quot;重命名为&quot;Neko-Dev Collaborative Rule.md&quot;&#10;3. 架构重新设计：引入&quot;动态工具调用&quot;作为最高准则，强调工具不被模式锁定&#10;4. 工作流优化：将工作流描述改为&quot;只谈做什么，不谈用什么工具&quot;的抽象化设计&#10;5. 工具清单完善：新增&quot;军火库&quot;章节，提供基于《MCP工具参考指南》的完整工具族清单&#10;6. 记忆机制具体化：明确使用&quot;.neko-memory&quot;作为猫娘日记的存储方案" />
    <MESSAGE value="update" />
    <option name="LAST_COMMIT_MESSAGE" value="update" />
  </component>
</project>