# 提示词

> 引导LLM的命令，LLM收到提示词会根据提示词和数据库中训练的数据，生成相关回应

# 提示词工程

> OpenAI优化提示词的六大策略

* Write clear instructions 编写清晰的说明
* Provide reference text 提供参考文本
* Split complex tasks into simpler subtasks 将复杂任务拆分为更简单的子任务
* Give the model time to "think" 给模型时间"思考"
* Use external tools 使用外部工具
* Test changes systemmatically 系统地测试变化

# 提示词使用技巧

#### 目标明确

> 模型无法预知提问的确切目标，需要告知其确切的目标

例如

- 如果输出太长，在提示词中编写要求简短回复

- 如果输出太简单，要求专家级内容编写

示例

* 写一首诗
* 写一首七言诗
* 写一首七言八句诗

#### 角色扮演

> 角色扮演可以更精确的引导模型理解需求，并生成更符合期望、具有特定风格和专业性的内容

示例

- 扮演一个历史学家，写一篇关于历史事件的文章
- 扮演一个程序员，写一篇关于编程技术的文章
- 你是一个工作十余年的java架构师，你有着丰富的架构经验，你可以快速的指出一个框架的设计缺陷，引导用户以更加符合守则与规范（如Kotlin开发准则，阿里开发规范等）的方式编写代码

> 角色扮演是让模型"知道"自己是谁，从而生成更符合期望的内容

#### 格式化输出

> 大部分情况下，LLM输出类似聊天的风格，为了提高输出的可读性，或便于对结果或过程进行加工处理，可以要求LLM进行格式化的输出，例如列表、表格、JSON、Markdown等

示例

- 早餐买什么
- 以列表的形式，输出早餐购买的清单
- 中国有哪些经典菜系
- 以表格的形式，输出中国经典菜系

#### 提供样本

> 提供样本可以引导模型理解需求，并生成更符合期望的内容。在提示词样本中，也称为少样本提示（Few-shot Prompting）,利用LLM的上下文学习能力（In-Context Learning），即模型能够在没有微调的情况下，通过上下文中提供的少量示例来学习和执行新任务

示例

- 指令：判断以下句子是正面情感还是负面情感："我今天的心情很糟糕。"

- 指令：判断以下句子是正面情感还是负面情感。以下是一些例子

  - 句子: 这电影太棒啦！

    > 情感: 正面

  - 句子: 我非常失望

    > 情感: 负面

  - 句子: 今天天气真好

    > 情感: 正面

  - 句子: 我感冒了，很难受

    > 情感: 负面

  - 句子: 我今天心情很好

    > 情感: ？

#### 思维链

> 在提示词中，要求模型分步骤解答问题，并展示其推理的整个过程，通过该方式可以减少不准确结果的可能性，也更容易评估模型的相应

示例

- 一个农场有鸡和牛共35头，脚共有94只。鸡和牛各有多少头。

- 一步一步计算下面的问题，并给出每一步的计算过程。第一次得到结果后，再增加一个步骤对结果进行验证。一个农场有鸡和牛共35头，脚共有94只。鸡和牛各有多少头。

#### 形成框架

- 角色（Role/Persona - 可选）: 明确扮演的角色
- 指令（Instruction/Task - 必须）: 清晰的说明希望模型做什么
- 背景/上下文（Context - 可选但强烈建议）: 提供必要的背景信息，帮助模型更好的理解任务
- 格式/限制（Format/Constraints - 可选）: 明确输出格式，长度限制，风格偏好
- 示例（Examples - 可选但非常有效）: 提供一些示例，帮助模型理解期望的输出