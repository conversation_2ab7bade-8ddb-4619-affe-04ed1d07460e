# Huazz Rules - AI工具与提示词规则

本项目收集和整理了用于AI编程助手的各种规则、提示词和工作流程，旨在提升AI协作开发的效率和质量。

## 📁 目录结构

```
huazz-rules/
├── README.md                   # 项目说明文档
├── mcp.json                    # MCP服务器配置文件
├── MCP工具参考指南.md              # MCP工具介绍和配置指南
└── 参考资料/                   # 所有参考资料和模板
    ├── 常用规则/               # 常用的提示词和规范
    │   ├── 主要提示词.md       # 主要的AI助手提示词
    │   ├── AI提交分析规范.md         # Git提交格式规范
    │   └── 使用指南.md         # 工具使用指南和经验分享
    ├── 提示词模板/             # 各种提示词模板
    │   ├── 猫娘v1.2.md         # 猫娘风格提示词v1.2
    │   ├── 猫娘v2.1.md         # 猫娘风格提示词v2.1
    │   └── 猫娘开发提示词.md   # 猫娘开发专用提示词
    ├── AI-Prompts/             # AI提示词相关研究
    │   ├── prompt-note.md      # 提示词使用笔记
    │   ├── Promptx工作机制.md  # Promptx工作原理
    │   ├── prompt增强版.md     # 增强版提示词
    │   ├── 提示词生成角色.md   # 提示词生成工具
    │   ├── README.md           # AI-Prompts说明
    │   └── 罗小涵智能体特调提示词/ # 特殊智能体配置
    ├── 工作流程/               # 开发工作流程
    │   ├── Augment Code 工作模式规范 (2025-06-03).md # 工具集标准化工作流模板
    │   ├── Augment Code 工作模式规范 (2025-06-18).md # 核心工作模式规范
    │   ├── Augment Code 工作模式规范 猫娘版(2025-06-23).md # 猫娘风格工作规范
    │   ├── 代码编辑任务工作流.md # 代码编辑流程
    │   ├── 技术研究任务工作流.md # 技术研究流程
    │   ├── 问题调试任务工作流.md # 调试工作流程
    │   └── 项目部署任务工作流.md # 部署流程
    ├── 其他工具/               # 其他辅助工具
    │   └── cunzhi.md           # 存知工具介绍
    ├── interactive-feedback mcp使用工作流.md # 交互反馈MCP使用说明
    ├── 其他参考.md             # 其他参考资料链接
    └── 来源.md                 # 统一的资源来源说明
```

## 🚀 快速开始

### MCP工具配置
1. 查看 `MCP工具参考指南.md` 了解MCP工具的功能和安装方式
2. 使用 `mcp.json` 配置文件设置MCP服务器
3. 根据需要调整配置参数（如DATA_DIR路径、授权密钥等）

### 提示词使用
1. 从 `参考资料/常用规则/主要提示词.md` 开始
2. 根据项目需求选择合适的提示词模板
3. 参考 `参考资料/常用规则/使用指南.md` 了解最佳实践

### 工作流程
1. 查看 `参考资料/工作流程/` 目录下的各种工作流程
2. 根据任务类型选择对应的工作流程模板
3. 参考 `参考资料/常用规则/AI提交分析规范.md` 规范Git提交

## 📋 主要功能

- **MCP工具集成**: 核心MCP工具的配置和使用指南
- **提示词模板**: 多种风格的AI助手提示词模板
- **工作流程**: 标准化的开发、调试、部署流程
- **使用指南**: 详细的工具使用说明和最佳实践
- **提交规范**: 统一的Git提交格式标准

## 🔧 工具列表

### MCP工具

以下是本项目配置的主要MCP工具。更详细的命令和用法，请参阅 [MCP工具参考指南.md](MCP工具参考指南.md)。

- **[server-sequential-thinking](https://smithery.ai/server/@smithery-ai/server-sequential-thinking)**: 提供动态和反思性的问题解决方法，通过结构化的思考过程来分析问题。
- **[deepwiki](https://mcp.deepwiki.com)**: 用于查询GitHub仓库的代码、文档和项目信息。
- **[shrimp-task-manager](https://github.com/cjo4m06/mcp-shrimp-task-manager)**: 专为AI Agent设计的智能任务系统，通过结构化流程将需求转化为开发任务。
- **[mcp-feedback-enhanced](https://lobehub.com/zh/mcp/minidoracat-mcp-feedback-enhanced)**: 建立面向反馈的开发工作流，支持桌面和Web双界面，极大提升AI协作效率。
- **[desktop-commander](https://desktopcommander.app/)**: 一个强大的本地开发助手，可以安全地与本地文件系统和操作系统进行交互。
- **[context7-mcp](https://github.com/upstash/context7-mcp)**: 查询和获取库与框架的最新官方文档，确保代码准确性。
- **[open-websearch](https://github.com/Aas-ee/open-webSearch)**: 支持多引擎的灵活网页搜索工具，无需API密钥。
- **[google-search](https://github.com/web-agent-master/google-search/blob/main/README.zh-CN.md)**: 通过本地浏览器模拟进行Google搜索，能有效绕过反爬虫机制。
- **playwright**: (需要额外配置) 提供浏览器自动化能力。
- **chrome-mcp-server**: (未启用) 用于与Chrome浏览器进行交互。

### 提示词类型
- **功能性提示词**: 专注于代码质量和开发效率
- **风格化提示词**: 如猫娘风格的趣味性提示词
- **专业化提示词**: 针对特定开发场景的专用提示词

## 📝 贡献指南

1. 按照项目结构添加新的工具或提示词
2. 更新对应的来源.md文件记录资源出处
3. 遵循现有的文件命名规范
4. 在README.md中更新相关信息

## 📄 许可证

本项目仅用于学习和研究目的，请遵循各资源的原始许可证。

## 🔗 相关链接

- [MCP官方文档](https://github.com/modelcontextprotocol)
- [Cursor AI编辑器](https://cursor.sh/)
- [AI提示词工程](https://www.promptingguide.ai/)

---

*最后更新时间: 2025-08-05*
